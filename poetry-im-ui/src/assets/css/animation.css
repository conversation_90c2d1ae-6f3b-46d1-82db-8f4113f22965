/* 以my-animation-开头的class */
[class*=my-animation-] {
    animation-duration: 1s;
    animation-timing-function: ease-out;
    animation-fill-mode: both;
}


.my-animation-slide-top {
    animation-name: slide-top
}

.my-animation-slide-bottom {
    animation-name: slide-bottom
}

.my-animation-hideToShow {
    animation-name: hideToShow
}


/* 上移 */
@keyframes slide-top {
    0% {
        opacity: 0;
        transform: translateY(-20%)
    }

    100% {
        opacity: 1;
        transform: translateY(0)
    }
}

/* 下移 */
@keyframes slide-bottom {
    0% {
        opacity: 0;
        transform: translateY(20%)
    }

    100% {
        opacity: 1;
        transform: translateY(0)
    }
}

/* 首图动画：下移 */
@keyframes header-effect {
    0% {
        opacity: 0;
        transform: translateY(-50px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 旋转 */
@keyframes rotate {
    0% {
        opacity: 1;
        transform: rotate(0deg);
    }

    100% {
        opacity: 1;
        transform: rotate(360deg);
    }
}

/* 显示 */
@keyframes hideToShow {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* 下移 */
@keyframes my-shake {
    0% {
        opacity: 1;
        transform: translateY(0px);
    }

    30% {
        opacity: 0.5;
        transform: translateY(25px);
    }

    100% {
        opacity: 1;
        transform: translateY(0px);
    }
}

/* 上移 */
@keyframes scatter {
    0% {
        top: 0;
    }

    50% {
        top: -15px;
    }

    100% {
        top: 0;
    }
}

/* 放大 */
@keyframes scale {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.2);
    }

    100% {
        transform: scale(1);
    }
}

/* 背景位置移动 */
@keyframes gradientBG {
    0% {
        background-position: 0 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}

/* 阴影变化 */
@keyframes weiYanShadowFlashing {
    0% {
        box-shadow: none;
    }

    50% {
        box-shadow: 0 0 20px var(--red);
    }

    100% {
        box-shadow: none;
    }
}
