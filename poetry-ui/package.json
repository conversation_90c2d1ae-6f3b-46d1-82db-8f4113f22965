{"name": "poetry-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.1", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "element-ui": "^2.4.5", "markdown-it": "^12.2.0", "markdown-it-multimd-table": "^4.2.3", "mavon-editor": "^2.9.1", "qs": "^6.10.3", "vue": "^2.6.11", "vue-baberrage": "^3.2.4", "vue-router": "^3.2.0", "vue-seamless-scroll": "^1.1.23", "vue-video-player": "^5.0.2", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^4.0.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "vue-cli-plugin-element": "^1.0.1", "vue-template-compiler": "^2.6.11"}}