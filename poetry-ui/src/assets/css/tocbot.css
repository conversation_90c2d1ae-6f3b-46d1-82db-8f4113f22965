.toc {
    width: 250px;
    position: fixed;
    top: calc(100vh / 4);
    right: 3vh;
    background: var(--commentContent);
    border-radius: 8px;
    z-index: 100;
    transition: all 1s;
    padding: 10px 10px 10px 5px;
    color: var(--maxGreyFont);
}

.toc::before {
    content: '🏖️目录';
    font-size: 24px;
    line-height: 40px;
    margin-left: 55px;
}

.toc > .toc-list {
    overflow-y: scroll;
    max-height: 52vh;
    position: relative;
    padding-inline-start: 35px !important;
    padding-right: 5px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.toc > .toc-list::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

.toc > .toc-list::-webkit-scrollbar-track {
    background-color: rgba(73, 177, 245, 0.2);
}

.toc > .toc-list::-webkit-scrollbar-thumb {
    background-color: var(--lightGreen);
    border-radius: 1em;
}

.toc .toc-list {
    padding-inline-start: 25px;
}

.toc a.toc-link {
    color: currentColor;
    height: 100%;
    text-decoration: none;
}

.toc .is-collapsible {
    max-height: 1000px;
    overflow: hidden;
    transition: all 300ms ease-in-out;
}

.toc .is-collapsed {
    max-height: 0;
}

.toc .toc-link {
    margin: 4px 0;
    display: block;
}

.toc .is-active-link {
    font-weight: 700;
    color: var(--white) !important;
    background: var(--lightGreen);
    padding: 4px 8px;
    border-radius: 5px;
}
