<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.dao.ResourcePathMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.entity.ResourcePath">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="classify" property="classify"/>
        <result column="cover" property="cover"/>
        <result column="url" property="url"/>
        <result column="introduction" property="introduction"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, classify, cover, url, introduction, type, status, remark, create_time
    </sql>

</mapper>
