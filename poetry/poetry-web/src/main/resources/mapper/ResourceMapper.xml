<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.dao.ResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.entity.Resource">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="type" property="type"/>
        <result column="size" property="size"/>
        <result column="mime_type" property="mimeType"/>
        <result column="original_name" property="originalName"/>
        <result column="path" property="path"/>
        <result column="store_type" property="storeType"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, type, size, mime_type, original_name, path, store_type, status, create_time
    </sql>

</mapper>
