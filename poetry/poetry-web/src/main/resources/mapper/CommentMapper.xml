<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.dao.CommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.entity.Comment">
        <id column="id" property="id"/>
        <result column="source" property="source"/>
        <result column="type" property="type"/>
        <result column="parent_comment_id" property="parentCommentId"/>
        <result column="user_id" property="userId"/>
        <result column="parent_user_id" property="parentUserId"/>
        <result column="like_count" property="likeCount"/>
        <result column="comment_content" property="commentContent"/>
        <result column="comment_info" property="commentInfo"/>
        <result column="floor_comment_id" property="floorCommentId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, source, type, parent_comment_id, user_id, parent_user_id, like_count, floor_comment_id, comment_content, comment_info, create_time
    </sql>

</mapper>
