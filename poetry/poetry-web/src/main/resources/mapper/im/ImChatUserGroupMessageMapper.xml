<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.im.http.dao.ImChatUserGroupMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.im.http.entity.ImChatUserGroupMessage">
        <id column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="from_id" property="fromId" />
        <result column="to_id" property="toId" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, group_id, from_id, to_id, content, create_time
    </sql>

</mapper>
