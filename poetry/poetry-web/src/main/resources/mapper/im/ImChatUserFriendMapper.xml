<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.im.http.dao.ImChatUserFriendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.im.http.entity.ImChatUserFriend">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="friend_id" property="friendId" />
        <result column="friend_status" property="friendStatus" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, friend_id, friend_status, remark, create_time
    </sql>

</mapper>
