package com.ld.poetry.im.http.service.impl;

import com.ld.poetry.im.http.entity.ImChatUserGroupMessage;
import com.ld.poetry.im.http.dao.ImChatUserGroupMessageMapper;
import com.ld.poetry.im.http.service.ImChatUserGroupMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 群聊记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Service
public class ImChatUserGroupMessageServiceImpl extends ServiceImpl<ImChatUserGroupMessageMapper, ImChatUserGroupMessage> implements ImChatUserGroupMessageService {

}
