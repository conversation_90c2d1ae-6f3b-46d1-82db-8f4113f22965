package com.ld.poetry.im.http.service.impl;

import com.ld.poetry.im.http.entity.ImChatGroup;
import com.ld.poetry.im.http.dao.ImChatGroupMapper;
import com.ld.poetry.im.http.service.ImChatGroupService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 聊天群 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Service
public class ImChatGroupServiceImpl extends ServiceImpl<ImChatGroupMapper, ImChatGroup> implements ImChatGroupService {

}
