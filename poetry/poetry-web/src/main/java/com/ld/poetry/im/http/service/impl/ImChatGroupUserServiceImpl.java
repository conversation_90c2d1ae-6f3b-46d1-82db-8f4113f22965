package com.ld.poetry.im.http.service.impl;

import com.ld.poetry.im.http.entity.ImChatGroupUser;
import com.ld.poetry.im.http.dao.ImChatGroupUserMapper;
import com.ld.poetry.im.http.service.ImChatGroupUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 聊天群成员 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Service
public class ImChatGroupUserServiceImpl extends ServiceImpl<ImChatGroupUserMapper, ImChatGroupUser> implements ImChatGroupUserService {

}
